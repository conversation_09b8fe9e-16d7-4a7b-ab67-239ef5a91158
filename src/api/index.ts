import _ from '@/utils/lodash'
import hy from './hy'
import { stringify } from 'querystringify'

const server = {
  nil: '', // 不拼接服务地址
  upms: '/upms', // 权限
  hazard: 'ehs-clnt-hazard-service', // 隐患
  inter: 'edu-inter-server',
  eduapp: 'edu-app-server',
  train: 'train-server',
  platform: 'ehs-clnt-platform-service', // 工作台
  apiHazard: 'ehs-api-hazard-service', // 工作台
}

export const $api = {
  type: server,
  name: _.merge(hy),

  getUrl(serviceType: string, apiName: string, query?: any): string {
    const paramsStr = query ? `?${stringify(query)}` : ''
    const _apiName = apiName.indexOf('/') === 0 ? apiName : '/' + apiName
    const _serviceType = serviceType || ''
    return `${_serviceType}${_apiName}${paramsStr}`
  },
}
// const server = {
//   nil: '', // 不拼接服务地址
// }

// export const $api = {
//   type: server,
//   name: _.merge(hy),

//   getUrl(serviceType: string, apiName: string): string {
//     const _apiName = apiName.indexOf('/') === 0 ? apiName : '/' + apiName
//     const _serviceType = serviceType ? '/' + serviceType : ''

//     return `${_serviceType}${_apiName}`
//   },
// }
