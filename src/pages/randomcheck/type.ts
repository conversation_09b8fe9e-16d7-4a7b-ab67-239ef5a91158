export type pageForm = {
  checkName: string
  checkStatus: '0' | '1' | '2' // 0:待开始 1：进行中 2：已完成
  checkStatusName: string
  checkUserJson: string
  commandUserJson: string
  createBy: string
  createTime: string
  endTime: string
  id: string
  objId: string
  objName: string
  pageNo: number
  pageSize: number
  search: string
  searchCount: number
  startTime: string
  unitId: string
  unitName: string
  updateBy: string
  updateTime: string
  zhId: string
  // 登录人
  createUserName: string
}
export type pageData<T> = {
  pageNo: number
  pageSize: number
  pages: number
  rows: T[]
  total: number
}
export type ResponsesData<T> = {
  rows: T[]
  total: number
  code: string
  data: T[]
  dataType: string
  message: object
  status: string
  token: string
}

export type addForms<T> = {
  /* 楼栋id */
  buildingId: string
  /* 创建者 */
  createBy: string
  createByName: string
  /* 创建时间 */
  createTime: string
  /* 处置id */
  disposeId: string
  /* 整改状态：0:待整改 1：已整改 2：整改中 */
  disposeState: string
  /* 整改状态：0:待整改 1：已整改 2：整改中 */
  disposeStateName: string
  /* 上报结束时间 */
  endTime: string
  /* 隐患检查项ID */
  essentialFactorClassItemId: string
  /* 隐患上报时间 */
  eventTime: string
  /* 隐患上报时间 */
  files: T[]
  hazardRandomCheckEventUsers: T[]
  /* 楼层id */
  floorId: string
  /* 隐患描述 */
  hazardDesc: string
  /* 隐患等级 */
  hazardLevel: string
  /* 隐患等级名称 */
  hazardLevelName: string
  /* 位置 */
  hazardPosition: string
  /* 隐患来源(1:物联网监测 2:人工上报 3:智能视频终端 4:监督检查隐患) 5:巡查检查 6:督导检查,7:专项检查,8:隐患自查,9:随机检查,10:隐患随手拍,11:设备点检,12:点位巡查,13:作业巡查 ,14、智能视频轮巡15、机器人自动巡检16、无人机自动巡检17、人工智能巡检18、人工上报（视频）19、人工上报（无人机）20、人工上报（机器人） */
  hazardSource: number
  /* 隐患来源名称 */
  hazardSourceName: string
  /* 隐患分类 */
  hazardType: string
  /* 隐患分类名称 */
  hazardTypeName: string
  /* 纬度 */
  latitude: number
  /* 经度 */
  longitude: number
  /* 百度坐标X */
  mapX: number
  /* 百度坐标Y */
  mapY: number
  /* 三维坐标Z */
  mapZ: number
  /* 随机检查表id */
  randomCheckId: string
  /* 整改人员  reformType reformUserName reformUserId */
  reformUserJson: T[]
  /* 备注 */
  remark: string
  /* 搜索条件 */
  search: string
  /* 上报开始时间 */
  startTime: string
  /* 分管单位ID */
  superviseUnitId: string
  /* 分管单位名称 */
  superviseUnitName: string
  /* 分管单位名称 */
  timeoutDays: number
  /* 单位ID */
  unitId: string
  /* 单位名称 */
  unitName: string
  /* 更新者 */
  updateBy: string
  /* 租户ID */
  zhId: string
  id: string
  /* 计划id */
  planId: string
  morehazardPlace: string
  /* 自定义字段获取隐患库id */
  typePid: string
  /* 自定义字段获取页面类型 */
  // pagetype: number
  // 隐患检查项ID---快捷上报
  checkAreaEssentialFactorId: string
  // ID-
  taskId: string
  hazardSourceNamevalue: string
  /* 整改期限 */
  correctionTime: string
  appBizType?: number
  appBizId?: string
}
