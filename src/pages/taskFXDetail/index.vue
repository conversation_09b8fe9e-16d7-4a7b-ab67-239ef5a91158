<route lang="json5">
{
  style: {
    navigationStyle: 'custom',
    navigationBarTitleText: '任务详情',
  },
}
</route>

<template>
  <view class="taskFx-detail-page">
    <SafetyNavbar title="任务详情"></SafetyNavbar>
    <CustomTabs
      class="w-full"
      :tabs="tabs"
      :activeIndex="activeIndex"
      @handleClick="handleChange"
    ></CustomTabs>
    <view class="page-main">
      <keep-alive>
        <component :is="curComp" />
      </keep-alive>
      <!-- <CheckDetail v-if="activeIndex === 0" />
      <HidenList v-if="activeIndex === 1" /> -->
    </view>
    <view class="page-bottom"></view>
  </view>
</template>

<script lang="ts" setup>
import SafetyNavbar from '../components/safety-navbar.vue'
import CustomTabs from '../components/custom-Tabs.vue'
import CheckDetail from './components/checkDetail/index.vue'
import HidenList from './components/hidenList/index.vue'

import { useUserStore } from '@/store'
import { debounce } from '@/utils'

defineOptions({ name: 'TaskFXDetail' })

const userStore = useUserStore()
const userInfo: any = userStore.userInfo ? userStore.userInfo : {}

const tabs = ['检查详情', '隐患清单']
const activeIndex = ref(0)

const beginRender = ref(false)
const curComp = computed(() => {
  return activeIndex.value === 0 ? markRaw(CheckDetail) : markRaw(HidenList)
})
// const curComp = markRaw(CheckDetail)

function handleChange(value) {
  activeIndex.value = value
  //   curComp.value = activeIndex.value === 0 ? CheckDetail : HidenList
}

onMounted(async () => {
  await nextTick()
  console.log('>>>>>123')

  //   setTimeout(() => {
  //     beginRender.value = true
  //   }, 800)
})
</script>

<style scoped lang="scss">
.taskFx-detail-page {
  @apply w-full h-full flex flex-col items-start gap-0;

  .page-main {
    @apply w-full min-h-0 flex-1 overflow-hidden;
    background: #f9f9f9;
  }

  .page-bottom {
    @apply w-full bg-yellow-400;
    height: 100rpx;
  }
}
</style>
