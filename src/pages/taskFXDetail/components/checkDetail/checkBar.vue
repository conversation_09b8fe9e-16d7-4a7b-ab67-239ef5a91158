<template>
  <view class="check-bar">
    <view v-for="item in barList" :key="item.id" :class="['bar-item', `bar-item_${item.id}`]">
      <text class="bar-name">{{ item.name }}</text>
      <text class="bar-value">{{ item.value }}</text>
    </view>
  </view>
</template>

<script lang="ts" setup>
defineOptions({ name: 'TaskFXCheckBar' })

const barList = ref([
  {
    id: '1',
    name: '排查点位数',
    value: 0,
  },
  {
    id: '2',
    name: '已排查检查点',
    value: 0,
  },
  {
    id: '3',
    name: '待开始检查点',
    value: 0,
  },
])

function getData() {}
</script>

<style scoped lang="scss">
.check-bar {
  @apply flex flex-row flex-nowrap items-center justify-between;
  gap: 16rpx;
  width: 100%;
  padding: 20rpx;
  margin: 20rpx 0 30rpx 0;
  background: #fff;
}
.bar-item {
  @apply flex flex-col items-center;
  padding: 16rpx 40rpx;
  border-radius: 16rpx;
  font-size: 24rpx;
  line-height: 36rpx;
  .bar-name {
    white-space: nowrap;
  }
  .bar-value {
    font-weight: 700;
    font-size: 44rpx;
    line-height: 48rpx;
  }
}
.bar-item_1 {
  background: #dee9ff;
  color: #0256ff;
}
.bar-item_2 {
  background: #dfffe9;
  color: #00b277;
}
.bar-item_3 {
  background: #f6f6f6;
  color: #a5a5a5;
}
</style>
