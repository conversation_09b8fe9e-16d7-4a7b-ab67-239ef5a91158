<template>
  <z-paging
    ref="paging"
    :fixed="false"
    :auto="false"
    v-model="listData"
    loading-more-no-more-text="我也是有底线的！"
    @query="getDataList"
  >
    <BaseInfo />
    <CheckBar />
    <view class="container">
      <view v-if="listData.length > 0">
        <view class="list-item" v-for="(item, index) in listData" :key="index">
          {{ item.name }}
        </view>
      </view>
      <template v-else>
        <EmptyComp />
      </template>
    </view>
  </z-paging>
</template>

<script lang="ts" setup>
import BaseInfo from './baseInfo.vue'
import CheckBar from './checkBar.vue'
import EmptyComp from '@/components/empty/index.vue'

import { debounce } from '@/utils'

defineOptions({ name: 'TaskFXCheckDetail' })

const paging = ref()

const listData = ref([])

function getDataList(pageNo, pageSize) {
  console.log(pageNo, pageSize)
  setTimeout(() => {
    console.log('>>>>', listData.value)

    for (let i = 0; i < pageSize; i++) {
      const id = (pageNo - 1) * pageSize + i
      console.log(id)
      listData.value.push({
        id,
        name: 'item_' + id,
      })
    }
    paging.value.completeByTotal(listData.value, 50)
  }, 600)
}

onMounted(async () => {
  await nextTick()
  console.log('onMounted>>>>', paging.value)
  //   paging.value.reload()
})
</script>

<style scoped lang="scss">
.container {
  @apply bg-blue;
}

.list-item {
  width: 100%;
  background-color: #fff;
  margin-bottom: 20rpx;
  font-size: 28rpx;
  color: #333;
  padding: 16rpx;
}
</style>
