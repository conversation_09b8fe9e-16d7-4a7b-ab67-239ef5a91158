<template>
  <view class="base-info">
    <view class="task-name">检查任务：延123风险排查（20250614）</view>
    <view class="task-tag">
      <!-- <view class="task-status">进行中</view> -->
      <!-- <view class="task-qx">正常</view> -->
      <wd-tag custom-class="tag-item" type="warning" mark>进行中</wd-tag>
      <wd-tag custom-class="tag-item" type="success" plain>正常</wd-tag>
    </view>
    <view class="info-item">
      <text class="label">检查对象：</text>
      <text class="value">899采气站</text>
    </view>
    <view class="info-item">
      <text class="label">检查负责人：</text>
      <text class="value">班组级</text>
    </view>
    <view class="info-item">
      <text class="label">检查参与人：</text>
      <view class="value value-cyr">
        <text class="cyr">{{ cyrText }}</text>
        <view class="value-more" @click="cyrOpen = !cyrOpen">
          {{ cyrOpen ? '收起' : '展开' }}
          <wd-icon
            :name="cyrOpen ? 'arrow-up' : 'arrow-down'"
            size="22px"
            color="#527cff"
          ></wd-icon>
        </view>
      </view>
    </view>
    <view class="info-item">
      <text class="label">任务起止时间：</text>
      <text class="value">2025-06-13 00:00:00~23:59:59</text>
    </view>
    <view class="info-item">
      <text class="label">检查要求：</text>
      <text class="value">检查要求检查要求检查要求检查要求,检查要求检查要求检查要求检查要求</text>
    </view>
  </view>
</template>

<script lang="ts" setup>
import { debounce } from '@/utils'

defineOptions({ name: 'TaskFXBaseInfo' })

const info = ref({})

const cyrOpen = ref(false)
const cyrText = computed(() => {
  const cyr = ['张三', '四', '王五', '王二小', '韩梅梅', '付帅', '李靖']
  return cyrOpen.value ? cyr.join('、') : cyr.slice(0, 3).join('、') + '...'
})

function getData() {}
</script>

<style scoped lang="scss">
.base-info {
  @apply w-full bg-[#fff];
  padding: 0 30rpx 20rpx 30rpx;

  .task-name {
    font-size: 32rpx;
    line-height: 1.5em;
    font-weight: 700;
    color: #000;
    margin-bottom: 20rpx;
  }

  .task-tag {
    @apply flex flex-row items-center justify-start;
    gap: 20rpx;
    margin-bottom: 20rpx;

    .tag-item {
      font-size: 28rpx;
      line-height: 1.4em;
    }
  }

  .info-item {
    @apply flex flex-row flex-nowrap items-start;
    gap: 20rpx;
    margin-bottom: 20rpx;
    font-size: 28rpx;
    color: #333;
    .label {
      white-space: nowrap;
      color: #7f7f7f;
    }
    .value: {
      flex: 1;
    }

    .value-cyr {
      @apply w-full flex flex-row flex-nowrap items-center justify-between;
      gap: 18rpx;
    }
    .value-more {
      @apply self-end flex flex-row flex-nowrap items-center;
      white-space: nowrap;
      font-size: 28rpx;
      color: #527cff;
    }
  }
}
</style>
