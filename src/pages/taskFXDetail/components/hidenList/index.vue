<template>
  <z-paging ref="pageRef" :fixed="false" v-model="listData" @query="getDataList">
    <template #top>
      <view>hidenList</view>
    </template>

    <view class="container">
      <view v-if="listData.length > 0">
        <view>hiden-item</view>
      </view>
      <template v-else>
        <EmptyComp />
      </template>
    </view>
  </z-paging>
</template>

<script lang="ts" setup>
import EmptyComp from '@/components/empty/index.vue'
import { debounce } from '@/utils'

defineOptions({ name: 'TaskFXHidenList' })

const pageRef = ref()

const listData = ref([])

function getDataList() {}
</script>

<style scoped lang="scss">
.container {
  @apply bg-blue;
}
</style>
