<!--
 * @Author: 悦悦 <EMAIL>
 * @Date: 2024-09-21 15:55:16
 * @LastEditors: 悦悦 <EMAIL>
 * @LastEditTime: 2024-11-12 17:18:49
 * @FilePath: /隐患排查app/src/pages/task/components/custom-card.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <wd-card
    v-for="item in cardsData"
    :key="item.taskId"
    style="background-color: #e8e8e85e"
    @click="selinfo(item)"
  >
    <template #title>
      <view class="title">
        <view class="title-planName">{{ item.planName }}</view>
        <view class="title-tip">
          <!-- <view class="title-tip-type">
            {{ item.planTypeName }}
          </view> -->
          <view
            class="title-tip-state"
            :style="
              item.taskState === '1'
                ? 'background-color: rgb(230, 162, 60)'
                : item.taskState === '2'
                  ? 'background-color:  rgb(2, 86, 255)'
                  : item.taskState === '3'
                    ? 'background-color:  rgb(103, 194, 58)'
                    : 'background-color:rgb(245, 108, 108)'
            "
          >
            {{ item.taskStateName || '已停用' }}
          </view>
        </view>
      </view>
    </template>
    <view class="content">
      <view>
        <view style="font-size: 16px; color: rgba(0, 0, 0, 0.85)">
          计划类型：{{ item.planTypeName || '--' }}
        </view>
        <view style="font-size: 16px; color: rgba(0, 0, 0, 0.85)">{{ item.unitNames }}</view>
      </view>
    </view>
    <view class="custominfo">
      <!-- 12819 【app检查任务】检查任务列表，只有检查频次为时需要展示时间，其他的不需要时分秒，只展示日期即可 -->
      <view style="color: rgba(0, 0, 0, 0.85)" v-if="item.frequencyType === '0'">
        {{ item.startEndTime }}
      </view>
      <view style="color: rgba(0, 0, 0, 0.85)" v-else>
        {{ formattedDate(item.planStartTime) }}~{{ formattedDate(item.planEndTime) }}
      </view>
      <view style="font-size: 16px; color: rgba(0, 0, 0, 0.85)">
        <wd-icon name="arrow-right" style="color: #0052d9" size="22px"></wd-icon>
      </view>
    </view>
  </wd-card>
</template>

<script lang="ts" setup>
// import props from '@/components/da-tree/props';
import dayjs from 'dayjs'
const props = defineProps<{
  cardsData: any[]
  tabindex?: number
}>()
// defineProps({
//   cardsData: {
//     type: Array<pageForm>,
//     default: [],
//   },
// })
// 时间图标按钮
function selinfo(val: any) {
  // console.log(props.tabindex, "=======");
  // console.log(val, '这是点击后获取的数据')
  const id = val.taskId
  const planId = val.planId
  // // console.log(new Date(val.planStartTime) < new Date())
  // console.log(new Date(val.planStartTime))
  if (dayjs(val.planStartTime) > dayjs() && val.taskState === '1') {
    // // console.log('计划未开始')
    uni.showToast({
      icon: 'none',
      title: '计划未开始',
    })
    return
  }
  // new Date(this.date1)
  let url
  // 1区域 2设备 3点位
  if (val.checkRange === '1') {
    url = `/pages/task/detail?taskId=${id}&planId=${planId}&tabindex=${props.tabindex}`
  } else {
    url = `/pages/task/equipmentcheckDetail?taskId=${id}&planId=${planId}&tabindex=${props.tabindex}`
  }
  uni.navigateTo({
    url,
  })
}
const formattedDate = (date) => dayjs(date).format('YYYY-MM-DD')
</script>

<style lang="scss" scoped>
::v-deep {
  .wd-card__footer {
    padding: 3px 0px;
  }
}

.content,
.title {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: flex-start;
  width: 100%;
}

.content {
  justify-content: flex-start;
  // height: 40px;
  min-height: 40px;
}

.title {
  justify-content: space-between;
}

.title-tip {
  display: flex;
  font-size: 12px;
  color: rgba(0, 0, 0, 0.25);
}

.wd-card {
  background-color: #ebeef5;
}

.custominfo {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
}

.title-planName {
  width: calc(100% - 150px);
  font-weight: 700;
  line-height: 25px;
}

.title-tip-type {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 6px 10px;
  margin-right: 8px;
  color: #fff;
  background-color: rgb(2, 86, 255);
  border-radius: 20px;
}

.title-tip-state {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 6px 10px;
  color: #fff;
  background-color: rgb(240, 240, 240);
  // border: 1px solid rgb(2, 86, 255);
  border-radius: 20px;
}
</style>
