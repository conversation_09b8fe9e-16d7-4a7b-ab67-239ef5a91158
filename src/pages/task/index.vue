<route lang="json5">
{
  style: {
    navigationStyle: 'custom',
    navigationBarTitleText: '检查任务',
  },
}
</route>

<template>
  <z-paging ref="paging" v-model="cardsData" @query="getDataList">
    <template #top>
      <SafetyNavbar title="检查任务"></SafetyNavbar>
      <view class="tabs-content">
        <CustomTabs
          :tabs="tabs"
          :activeIndex="activeIndex"
          @handleClick="handleChange"
        ></CustomTabs>
        <!-- 计划类型标签展示 -->
        <view v-if="planTypeList.length > 0">
          <scroll-view scroll-x="true" class="plan-type-scroll">
            <view class="plan-type-container">
              <view
                v-for="(item, index) in planTypeList"
                :key="index"
                class="plan-type-item"
                :class="{ active: planType === item.value }"
                @click="handlePlanTypeClick(item)"
              >
                {{ item.label }}
              </view>
            </view>
          </scroll-view>
        </view>
        <wd-search
          placeholder-left
          @change="search"
          placeholder="请输入任务名称模糊搜索"
          :hide-cancel="true"
          v-model="params.keyWords"
          @clear="clear"
        />
        <wd-drop-menu>
          <wd-drop-menu-item
            title="任务状态"
            v-model="taskState"
            :options="taskStateList"
            @change="handleChangeTaskState"
          />
          <wd-drop-menu-item title="单位名称" ref="dropMenu">
            <wd-search
              placeholder-left
              @change="searchunitname"
              placeholder="单位名称"
              :hide-cancel="true"
              v-model="likeFieldValue"
              @clear="clear"
            />
            <view style="width: 90%; margin: auto">
              <wd-radio-group
                v-if="checkUnitIdList.length > 0"
                v-model="checkUnitId"
                @change="handleChangeCheckUnitId"
              >
                <scroll-view style="height: 30vh" scroll-y="true">
                  <wd-radio
                    v-for="(itme, index) in checkUnitIdList"
                    :key="index"
                    :value="itme.value"
                  >
                    {{ itme.label }}
                  </wd-radio>
                </scroll-view>
              </wd-radio-group>
              <view v-else class="flex items-center justify-center mt-[2%]">
                <view class="relative">
                  <img src="@/static/images/no-data.png" width="237px" height="165px" />
                  <text
                    class="absolute bottom-[20px] left-[0] w-[237px] text-center color-[#969799]"
                  >
                    暂无数据
                  </text>
                </view>
              </view>
            </view>
          </wd-drop-menu-item>
          <!-- <wd-drop-menu-item title="检查对象" v-model="checkUnitId" :options="checkUnitIdList"
              @change="handleChangeCheckUnitId" /> -->
        </wd-drop-menu>
      </view>
    </template>

    <view class="container">
      <view v-if="cardsData.length">
        <view v-if="activeIndex === 0">
          <customCard :tabindex="activeIndex" :cardsData="cardsData"></customCard>
        </view>
        <view v-if="activeIndex === 1">
          <customCard :tabindex="activeIndex" :cardsData="cardsData"></customCard>
        </view>
      </view>
      <template v-else>
        <view class="list-null">
          <noData title="暂无数据~"></noData>
        </view>
      </template>
    </view>
  </z-paging>
</template>

<script lang="ts" setup>
import SafetyNavbar from '../components/safety-navbar.vue'
import CustomTabs from '../components/custom-Tabs.vue'
import customCard from './components/custom-card.vue'
import { pageForm } from './type'
import { onPageScroll, onReachBottom } from '@dcloudio/uni-app'
import {
  getAllUnit,
  taskDetail,
  taskInspection,
  unitData,
  getCheckTypeDataList,
  getUserTaskUnitList,
} from './featch'
import { useUserStore } from '@/store'
import { debounce } from '@/utils'
const userStore = useUserStore()
const userInfo: any = userStore.userInfo ? userStore.userInfo : {}
const tabs = ref([])
const activeIndex = ref(0)
const total = ref(0)
const noDate = ref<boolean>(false)
const cardsData = ref<any[]>([])
const paging = ref()
// 下拉菜单
const planType = ref<any>('0000')
const taskState = ref<number>(0)
const checkUnitId = ref<number>(0)
const planTypeList = ref<any>([])
const taskStateList = ref<any>([
  { label: '全部', value: 0 },
  { label: '待开始', value: 1 },
  { label: '进行中', value: 2 },
  { label: '已完成', value: 3 },
  // { label: '已停用', value: 4 },
  // 已停用
])
const checkUnitIdList = ref<any>([{ label: '全部', value: 0 }])
const checkUnitIdListAll = ref<any>([])
const likeFieldValue = ref('')
const params = ref<pageForm>({
  keyWords: '',
  taskType: '',
  pageNo: 1,
  pageSize: 10,
  planEndDate: '',
  planStartDate: '',
  planTypeId: '',
  searchCount: 0,
  superviseUnitId: '',
  taskState: '1',
  unitId: userInfo.unitId,
  planUserId: userInfo.id, // 当前登陆人id
  userRoleCodes: '',
  planTypeName: '',
})

// 计划类型标签点击处理
function handlePlanTypeClick(item) {
  planType.value = item.value
  params.value.pageNo = 1
  total.value = 0
  cardsData.value = []
  paging.value.reload()
}

function handleChangePlanType() {
  params.value.pageNo = 1
  total.value = 0
  cardsData.value = []
  // getDataList()
  paging.value.reload()
}
function handleChangeTaskState() {
  params.value.pageNo = 1
  total.value = 0
  cardsData.value = []
  // getDataList()
  paging.value.reload()
}
function handleChangeCheckUnitId() {
  params.value.pageNo = 1
  total.value = 0
  cardsData.value = []
  // getDataList()
  paging.value.reload()
}

function getDataList(pageNo) {
  // console.log(pageNo, 'params===????')
  uni.showLoading({
    mask: true,
  })
  params.value.pageNo = pageNo
  taskInspection({
    ...params.value,
    planTypeName: planType.value === '0000' ? null : planType.value,
    taskState: taskState.value === 0 ? null : taskState.value,
    checkUnitId: checkUnitId.value === 0 ? null : checkUnitId.value,
    planUserId: userInfo.id,
  })
    .then((res: any) => {
      uni.hideLoading()
      // console.log(res, '列表数据------')
      if ((res.code as any) === 'success') {
        total.value = res.data.total
        // paging.value.complete(res.data.rows)
        paging.value.completeByTotal(res.data.rows, total.value)
      }
    })
    .catch(() => {
      uni.hideLoading()
      paging.value.complete(false)
    })
    .finally(() => {
      uni.hideLoading()
      // console.log(111)
    })
}

//  组织机构获取单位列表
function getAllUnitAPI() {
  const params = {
    orgCode: userInfo.unitId,
    pageSize: -1,
  }
  return new Promise((resolve) => {
    getAllUnit(params as any).then((res: any) => {
      // console.log(res)
      res.data.rows.forEach((item) => {
        checkUnitIdList.value.push({
          label: item.unitName,
          value: item.id,
        })
      })
      checkUnitIdListAll.value = checkUnitIdList.value
      resolve(res)
    })
  })
}

// 相关方/承租方获取单位
function getUserByunit() {
  return new Promise((resolve) => {
    getUserTaskUnitList({ userId: userInfo.id }).then((res: any) => {
      res.data.rows.forEach((item) => {
        checkUnitIdList.value.push({
          label: item.unitName,
          value: item.id,
        })
      })
      checkUnitIdListAll.value = checkUnitIdList.value
      resolve(res)
    })
  })
}

const searchunitname = debounce(() => {
  checkUnitIdList.value = checkUnitIdListAll.value.filter((itme) => {
    return itme.label.includes(likeFieldValue.value)
  })
}, 500)

const getCheckTypeData = () => {
  const params = {
    planTypeStatus: '1',
    // unitId: userInfo.topUnitId,
    unitId: +userInfo.orgRes === 1 ? userInfo.topUnitId : userInfo.serverUnitId,
  }
  // console.log(params, 7777)

  return new Promise((resolve) => {
    getCheckTypeDataList(params as any).then((res: any) => {
      // console.log(res, 888)
      if (res.data.length) {
        res.data.forEach((el) => {
          el.label = el.planTypeName
          el.value = el.planTypeName
        })
      }
      res.data.unshift({ label: '全部', value: '0000' })
      planTypeList.value = res.data
      resolve(res)
    })
  })
}
// 新需求----计划类型
getCheckTypeData()

// const scrolltolower = () => {
//   // console.log('底部')
//   if (cardsData.value.length < total.value) {
//     params.pageNo++
//     getDataList()
//   } else {
//     uni.showToast({
//       title: '没有更多数据了',
//       icon: 'none',
//       duration: 2000,
//     })
//   }
// }
function handleChange(event) {
  // console.log('父组件取子组件的值', event)
  if (userInfo.orgRes === '1') {
    params.value.taskType = event === 0 ? 1 : '' // 我执行的任务
  } else {
    params.value.taskType = 1 /// / 我执行的任务
  }
  // params.value.taskType = event === 1 ? 1 : '' // 我执行的任务
  activeIndex.value = event
  params.value.pageNo = 1
  planType.value = '0000'
  // total.value = 0
  cardsData.value = []
  paging.value.reload()
  // getDataList()
}

onLoad((urlParams) => {
  console.log('onLoad', urlParams)

  params.value.pageNo = 1
  // total.value = 0
  cardsData.value = []
  // 我执行的任务
  params.value.taskType = 1
  // getDataList()
  // '计划任务', '我执行的任务'
  // 判断当前登录人 是否是组织机构 1：组织机构 2/3:相关方和承租方
  if (userInfo.orgRes === '1') {
    tabs.value = ['我执行的任务', '计划任务跟踪']
    activeIndex.value = urlParams?.tab ? +urlParams?.tab : 0
    getAllUnitAPI()
  } else {
    tabs.value = ['我执行的任务']
    getUserByunit()
  }
})
onReachBottom(() => {
  // console.log(params.value.pageNo, 'params.pageNo')
  if (cardsData.value.length >= total.value) return
  params.value.pageNo++
  // // console.log(params.value.pageNo, 'params.pageNo')
  // getDataList()
  paging.value.reload()
})
onShow(() => {
  // 局部更新数据状态
  const taskid = uni.getStorageSync('taskIdByindex') ? uni.getStorageSync('taskIdByindex') : ''
  if (taskid !== '') {
    taskDetail(taskid).then((res: any) => {
      if (+res.data.delFlag === 1) {
        paging.value.reload()
      }
      if (res.data.taskState === '1') {
        updateValueById(taskid, '待开始', res.data.taskState)
      } else if (res.data.taskState === '2') {
        updateValueById(taskid, '进行中', res.data.taskState)
      } else if (res.data.taskState === '3') {
        updateValueById(taskid, '已完成', res.data.taskState)
      }
    })
  }
})
const updateValueById = (id: string, newValue: string, newState: string) => {
  // // console.log(cardsData.value)
  const item = cardsData.value.find((item) => item.taskId === id)
  // // console.log(item, '===============改变值')
  if (item) {
    item.taskStateName = newValue
    item.taskState = newState
  } else {
    console.warn(`Item with id ${id} not found`)
  }
}
// 输入框模糊查询
let timeoutId: ReturnType<typeof setTimeout> | null = null
function search(event) {
  if (timeoutId !== null) {
    clearTimeout(timeoutId)
    timeoutId = null
  }
  timeoutId = setTimeout(() => {
    params.value.pageNo = 1
    // total.value = 0
    cardsData.value = []
    paging.value.reload() // 刷新
    // getDataList()
  }, 500)
}

// 清空输入框
function clear() {
  params.value.keyWords = ''
}

onUnload(() => {
  uni.removeStorageSync('planId')
  uni.removeStorageSync('unitList')
  uni.removeStorageSync('taskIdByindex')
})
</script>

<style lang="scss">
::v-deep {
  .wd-search {
    background: none !important;
    border: none !important;
  }

  .zp-page-top {
    background: white !important;
  }

  .navbar {
    position: unset !important;
  }
}

.container {
  position: relative;
}

.borderBox {
  border-top: 0.0625rem solid #ebebeb;
}

// .tabs-content {}

.custominfo {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.no-date {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100px;
  color: rgb(125, 125, 125);
}

.placeholder {
  height: 40px;
}

.plan-type-scroll {
  white-space: nowrap;
}

.plan-type-container {
  display: flex;
  flex-direction: row;
  justify-content: space-around;
  align-items: center;
  gap: 4px;
  padding: 0 15px;
}

.plan-type-item {
  display: inline-block;
  padding: 2px 8px;
  background-color: #f5f5f5;
  border-radius: 10px;
  font-size: 14px;
  color: #666;
  white-space: nowrap;
  transition: all 0.3s ease;
  border: 1px solid transparent;
}

.plan-type-item.active {
  background-color: #4285f4;
  color: #fff;
  border-color: #4285f4;
}

.plan-type-item:active {
  opacity: 0.8;
}
</style>
