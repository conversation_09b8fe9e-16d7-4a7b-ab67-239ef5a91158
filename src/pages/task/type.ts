/*
 * @Author: 悦悦 <EMAIL>
 * @Date: 2024-09-21 15:55:16
 * @LastEditors: 悦悦 <EMAIL>
 * @LastEditTime: 2024-09-23 20:08:55
 * @FilePath: /隐患排查app/src/pages/task/type.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */

// export type pageData<T> = {
//   pageNo: number
//   pageSize: number
//   pages: number
//   rows: T[]
//   total: number
// }

// 检查任务列表
export type pageForm = {
  keyWords: string
  pageNo: number
  pageSize: number
  planEndDate: string
  planStartDate: string
  planTypeId: string
  planTypeName: string
  planUserId: string
  searchCount: number
  superviseUnitId: string
  taskState: '1' | '2' | '3' // 1:待开始 2：进行中 3：已完成
  unitId: string
  userRoleCodes: string
  taskType: number | string
}

export type recordList = {
  taskId: string
  userId: string
  userName: string
}

export type taskAdd = {
  clockInAddress: string
  filePath: string
  taskId: string
  userId: string
  userName: string
}

export type check = {
  checkContent: string
  checkContentId: string
  checkState: string
  checkType: string
}
